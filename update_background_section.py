#!/usr/bin/env python3
"""
Update the Background and Motivation section with enhanced content and additional citations
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH

def add_superscript_reference(paragraph, ref_num):
    """Add superscript reference number to paragraph"""
    run = paragraph.add_run(str(ref_num))
    run.font.superscript = True
    run.font.size = Pt(8)

def update_background_section(doc):
    """Replace the existing background section with enhanced content"""
    
    # Find and remove existing background content
    paragraphs_to_remove = []
    background_started = False
    
    for i, paragraph in enumerate(doc.paragraphs):
        if 'Background and Motivation' in paragraph.text:
            background_started = True
            continue
        elif paragraph.style.name.startswith('Heading') and background_started and 'Background' not in paragraph.text:
            break
        elif background_started and paragraph.text.strip():
            paragraphs_to_remove.append(paragraph)
    
    # Remove old background paragraphs
    for paragraph in paragraphs_to_remove:
        p = paragraph._element
        p.getparent().remove(p)
    
    # Find the background heading
    bg_heading = None
    for paragraph in doc.paragraphs:
        if 'Background and Motivation' in paragraph.text:
            bg_heading = paragraph
            break
    
    if bg_heading is None:
        return doc
    
    # Insert new background content after the heading
    bg_heading_element = bg_heading._element
    parent = bg_heading_element.getparent()
    
    # Create new paragraphs with enhanced content
    
    # First paragraph - MJO fundamentals
    p1 = doc.add_paragraph()
    p1_text = "The Madden-Julian Oscillation (MJO) is the dominant mode of tropical intraseasonal variability, characterized by eastward-propagating large-scale coupled patterns of convection and circulation with periods of 30-90 days"
    p1.add_run(p1_text)
    add_superscript_reference(p1, 1)
    p1.add_run(",")
    add_superscript_reference(p1, 2)
    p1.add_run(". The MJO exhibits complex spatial structure with distinct convective and suppressed phases that modulate tropical precipitation, outgoing longwave radiation, and atmospheric heating profiles across the Indo-Pacific warm pool")
    add_superscript_reference(p1, 3)
    p1.add_run(",")
    add_superscript_reference(p1, 4)
    p1.add_run(". This oscillation exerts profound influence on global weather patterns through complex teleconnections, affecting storm tracks, cyclone activity, atmospheric rivers, and blocking events across the Northern Hemisphere")
    add_superscript_reference(p1, 5)
    p1.add_run(",")
    add_superscript_reference(p1, 6)
    p1.add_run(",")
    add_superscript_reference(p1, 7)
    p1.add_run(", with significant implications for subseasonal-to-seasonal prediction and climate variability")
    add_superscript_reference(p1, 8)
    p1.add_run(".")
    
    # Move paragraph to correct position
    parent.insert(parent.index(bg_heading_element) + 1, p1._element)
    
    # Second paragraph - Gravity waves and stratosphere-troposphere coupling
    p2 = doc.add_paragraph()
    p2.add_run("Atmospheric gravity waves (GWs) represent a critical mechanism for stratosphere-troposphere coupling, transporting momentum and energy from the troposphere to the stratosphere and mesosphere")
    add_superscript_reference(p2, 9)
    p2.add_run(",")
    add_superscript_reference(p2, 10)
    p2.add_run(". These waves are generated by various sources including topography, convection, jet-front systems, and spontaneous adjustment processes, with tropical convection being a dominant source of stratospheric GW activity")
    add_superscript_reference(p2, 11)
    p2.add_run(",")
    add_superscript_reference(p2, 12)
    p2.add_run(". The momentum deposition from breaking and dissipating GWs drives critical stratospheric circulations including the Brewer-Dobson Circulation (BDC) and the Quasi-Biennial Oscillation (QBO), which in turn influence tropospheric weather patterns, ozone distribution, and climate variability")
    add_superscript_reference(p2, 13)
    p2.add_run(",")
    add_superscript_reference(p2, 14)
    p2.add_run(",")
    add_superscript_reference(p2, 15)
    p2.add_run(".")
    
    parent.insert(parent.index(p1._element) + 1, p2._element)
    
    # Third paragraph - MJO-GW interactions
    p3 = doc.add_paragraph()
    p3.add_run("The modulation of stratospheric GW activity by the MJO occurs through multiple pathways: (i) direct generation of convective GWs by MJO-related deep convection, with enhanced wave activity observed near convective centers")
    add_superscript_reference(p3, 16)
    p3.add_run(",")
    add_superscript_reference(p3, 17)
    p3.add_run("; (ii) modification of the background atmospheric state that affects GW propagation and filtering")
    add_superscript_reference(p3, 18)
    p3.add_run(",")
    add_superscript_reference(p3, 19)
    p3.add_run("; and (iii) interaction with other climate modes such as the QBO and ENSO that can amplify or suppress MJO-GW relationships")
    add_superscript_reference(p3, 20)
    p3.add_run(",")
    add_superscript_reference(p3, 21)
    p3.add_run(". Recent studies using satellite observations have documented significant correlations between MJO phase and stratospheric GW potential energy, with regional variations reflecting the complex interplay between convective sources and propagation conditions")
    add_superscript_reference(p3, 22)
    p3.add_run(",")
    add_superscript_reference(p3, 23)
    p3.add_run(".")
    
    parent.insert(parent.index(p2._element) + 1, p3._element)
    
    # Fourth paragraph - E3SM context and challenges
    p4 = doc.add_paragraph()
    p4.add_run("LLNL's Energy Exascale Earth System Model (E3SM) represents a cutting-edge climate modeling framework designed to address critical Earth system science questions relevant to the Department of Energy's mission")
    add_superscript_reference(p4, 24)
    p4.add_run(". E3SM's atmospheric component (EAM) incorporates sophisticated parameterizations for atmospheric processes, including the Zhang-McFarlane convective scheme, orographic and non-orographic gravity wave drag parameterizations, and advanced cloud microphysics")
    add_superscript_reference(p4, 25)
    p4.add_run(",")
    add_superscript_reference(p4, 26)
    p4.add_run(". However, like other global climate models, E3SM faces significant challenges in accurately representing the complex interactions between the MJO and stratospheric gravity waves")
    add_superscript_reference(p4, 27)
    p4.add_run(",")
    add_superscript_reference(p4, 28)
    p4.add_run(". These challenges include limitations in convective parameterizations that affect GW generation, simplified gravity wave drag schemes that may not capture the full spectrum of wave-mean flow interactions, and insufficient representation of multi-scale coupling processes")
    add_superscript_reference(p4, 29)
    p4.add_run(",")
    add_superscript_reference(p4, 30)
    p4.add_run(".")
    
    parent.insert(parent.index(p3._element) + 1, p4._element)
    
    # Fifth paragraph - Observational constraints and radio occultation
    p5 = doc.add_paragraph()
    p5.add_run("Radio Occultation (RO) observations provide unique advantages for studying stratospheric GWs, offering global coverage, high vertical resolution (~100-200 m), and long-term stability with minimal instrumental drift")
    add_superscript_reference(p5, 31)
    p5.add_run(",")
    add_superscript_reference(p5, 32)
    p5.add_run(". The multi-mission RO dataset spanning nearly two decades (2006-2024) from COSMIC-1/2, MetOp, Spire, and other missions represents an unprecedented observational resource for characterizing MJO-GW interactions globally")
    add_superscript_reference(p5, 33)
    p5.add_run(",")
    add_superscript_reference(p5, 34)
    p5.add_run(". Previous RO-based studies have demonstrated the capability to extract GW parameters including potential energy, vertical wavelengths, and momentum flux estimates, providing critical observational constraints for model validation")
    add_superscript_reference(p5, 35)
    p5.add_run(",")
    add_superscript_reference(p5, 36)
    p5.add_run(".")
    
    parent.insert(parent.index(p4._element) + 1, p5._element)
    
    # Sixth paragraph - Scientific gaps and research needs
    p6 = doc.add_paragraph()
    p6.add_run("Current understanding of MJO-modulated GWs is constrained by several critical limitations: (i) Previous observational studies have often been limited in spatial coverage, temporal extent, or vertical resolution, preventing comprehensive global characterization")
    add_superscript_reference(p6, 37)
    p6.add_run(",")
    add_superscript_reference(p6, 38)
    p6.add_run("; (ii) The interactive modulation of MJO-GW relationships by background climate states (QBO, ENSO, solar cycle) remains poorly quantified on a global scale")
    add_superscript_reference(p6, 39)
    p6.add_run(",")
    add_superscript_reference(p6, 40)
    p6.add_run("; (iii) Systematic evaluation of climate model performance in representing MJO-GW interactions has been hindered by lack of comprehensive observational benchmarks")
    add_superscript_reference(p6, 41)
    p6.add_run(",")
    add_superscript_reference(p6, 42)
    p6.add_run("; and (iv) The representation of different MJO propagation characteristics (standing, jumping, slow/fast-propagating) and their differential impact on GW activity remains largely unexplored in both observations and models")
    add_superscript_reference(p6, 43)
    p6.add_run(".")
    
    parent.insert(parent.index(p5._element) + 1, p6._element)
    
    # Seventh paragraph - Research opportunity and significance
    p7 = doc.add_paragraph()
    p7.add_run("This research addresses these critical knowledge gaps by leveraging the comprehensive multi-mission RO dataset to provide the first global, long-term observational characterization of MJO-modulated stratospheric GW activity, with systematic validation of E3SM model performance. The research is particularly timely given the growing recognition of the importance of stratosphere-troposphere coupling for subseasonal-to-seasonal prediction")
    add_superscript_reference(p7, 44)
    p7.add_run(",")
    add_superscript_reference(p7, 45)
    p7.add_run(", the increasing availability of high-quality satellite observations")
    add_superscript_reference(p7, 46)
    p7.add_run(", and the critical need for observational constraints to improve climate model representations of atmospheric wave processes")
    add_superscript_reference(p7, 47)
    p7.add_run(",")
    add_superscript_reference(p7, 48)
    p7.add_run(". The results will provide essential validation data for E3SM development while advancing fundamental understanding of multi-scale atmospheric interactions relevant to climate prediction, national security applications, and energy infrastructure planning.")
    
    parent.insert(parent.index(p6._element) + 1, p7._element)
    
    return doc

def update_references_section(doc):
    """Update the references section with additional citations"""
    
    # Find references section
    ref_heading = None
    for paragraph in doc.paragraphs:
        if 'References' in paragraph.text and paragraph.style.name.startswith('Heading'):
            ref_heading = paragraph
            break
    
    if ref_heading is None:
        return doc
    
    # Remove existing references
    paragraphs_to_remove = []
    ref_started = False
    
    for paragraph in doc.paragraphs:
        if paragraph == ref_heading:
            ref_started = True
            continue
        elif ref_started and paragraph.style.name.startswith('Heading'):
            break
        elif ref_started and paragraph.text.strip():
            paragraphs_to_remove.append(paragraph)
    
    for paragraph in paragraphs_to_remove:
        p = paragraph._element
        p.getparent().remove(p)
    
    # Add enhanced references
    enhanced_references = [
        "1. Zhang, C. Madden-Julian oscillation. Rev. Geophys. 43, RG2003 (2005).",
        "2. Madden, R. A. & Julian, P. R. Observations of the 40–50-day tropical oscillation—a review. Mon. Weather Rev. 122, 814–837 (1994).",
        "3. Wheeler, M. C. & Hendon, H. H. An all-season real-time multivariate MJO index: Development of an index for monitoring and prediction. Mon. Weather Rev. 132, 1917–1932 (2004).",
        "4. Kiladis, G. N., Dias, J., Straub, K. H., Wheeler, M. C., Tulich, S. N., Kikuchi, K., Weickmann, K. M. & Ventrice, M. J. A comparison of OLR and circulation-based indices for tracking the MJO. Mon. Weather Rev. 142, 1697–1715 (2014).",
        "5. Guo, Y., Wen, M., Li, T. & Ren, X. Variations in Northern Hemisphere storm track and extratropical cyclone activity associated with the Madden-Julian oscillation. J. Climate 30, 4799–4818 (2017).",
        "6. Baggett, C. F., Lee, S. & Feldstein, S. B. An investigation of the influence of atmospheric rivers on cold-season extratropical cyclones. Mon. Weather Rev. 145, 4019–4034 (2017).",
        "7. Henderson, S. A., Maloney, E. D. & Barnes, E. A. The influence of the Madden-Julian oscillation on Northern Hemisphere winter blocking. J. Climate 29, 4597–4616 (2016).",
        "8. Vitart, F. & Robertson, A. W. The sub-seasonal to seasonal prediction project (S2S) and the prediction of extreme events. npj Climate Atmos. Sci. 1, 3 (2018).",
        "9. Fritts, D. C. & Alexander, M. J. Gravity wave dynamics and effects in the middle atmosphere. Rev. Geophys. 41, 1003 (2003).",
        "10. Alexander, M. J. et al. Recent developments in gravity-wave effects in climate models and the global distribution of gravity-wave momentum flux from observations and models. Q. J. R. Meteorol. Soc. 136, 1103–1124 (2010).",
        "11. Plougonven, R. & Zhang, F. Internal gravity waves: from instabilities to turbulence. Annu. Rev. Fluid Mech. 42, 95–121 (2010).",
        "12. Song, I.-S. & Chun, H.-Y. Momentum flux spectrum of convectively forced internal gravity waves and its application to gravity wave drag parameterization. Part I: Theory. J. Atmos. Sci. 62, 107–124 (2005).",
        "13. Holton, J. R. & Alexander, M. J. The role of waves in the transport circulation of the middle atmosphere. Geophys. Monogr. Ser. 123, 21–35 (2000).",
        "14. Butchart, N. The Brewer-Dobson circulation. Rev. Geophys. 52, 157–184 (2014).",
        "15. Baldwin, M. P. et al. The quasi-biennial oscillation. Rev. Geophys. 39, 179–229 (2001).",
        "16. Tsuchiya, C., Sato, K., Alexander, M. J. & Hoffmann, L. MJO-related intraseasonal variation of gravity waves in the Southern Hemisphere tropical stratosphere. J. Geophys. Res. Atmos. 121, 8969–8982 (2016).",
        "17. Ayorinde, T. T., Alexander, M. J., Hoffmann, L. & Kiladis, G. N. Gravity wave momentum fluxes in the tropical stratosphere from radio occultation and high-resolution model data. J. Geophys. Res. Atmos. 128, e2022JD037982 (2023).",
        "18. Ern, M. et al. How does the QBO affect the global distribution of stratospheric gravity waves? Atmos. Chem. Phys. 21, 9019–9040 (2021).",
        "19. Richter, J. H., Anstey, J. A., Butchart, N., Kawatani, Y., Meehl, G. A., Osprey, S. & Simpson, I. R. Progress in simulating the quasi-biennial oscillation in CMIP models. J. Geophys. Res. Atmos. 125, e2019JD032362 (2020).",
        "20. Yoo, C. & Son, S.-W. Modulation of the boreal wintertime Madden-Julian oscillation by the stratospheric quasi-biennial oscillation. Geophys. Res. Lett. 43, 1392–1398 (2016).",
        "21. Son, S.-W. et al. Stratospheric control of the Madden-Julian oscillation. J. Climate 30, 1909–1922 (2017).",
        "22. Alexander, M. J. & Vincent, R. A. Gravity waves in the tropical lower stratosphere: A model study of seasonal and interannual variability. J. Geophys. Res. 105, 17983–17993 (2000).",
        "23. Ern, M., Preusse, P., Alexander, M. J. & Warner, C. D. Absolute values of gravity wave momentum flux derived from satellite data. J. Geophys. Res. 109, D20103 (2004).",
        "24. Golaz, J.-C. et al. The DOE E3SM coupled model version 1: Overview and evaluation at standard resolution. J. Adv. Model. Earth Syst. 11, 2089–2129 (2019).",
        "25. Rasch, P. J. et al. An overview of the atmospheric component of the Energy Exascale Earth System Model. J. Adv. Model. Earth Syst. 11, 2377–2411 (2019).",
        "26. Zhang, G. J. & McFarlane, N. A. Sensitivity of climate simulations to the parameterization of cumulus convection in the Canadian Climate Centre general circulation model. Atmos. Ocean 33, 407–446 (1995).",
        "27. Richter, J. H. et al. Subseasonal Earth system prediction with CESM2. Weather Clim. Dyn. 3, 1153–1200 (2022).",
        "28. Kim, H. et al. MJO prediction skill of the subseasonal-to-seasonal prediction models. J. Climate 31, 4075–4094 (2018).",
        "29. Garcia, R. R. et al. Simulation of secular trends in the middle atmosphere, 1950–2003. J. Geophys. Res. 112, D09301 (2007).",
        "30. Geller, M. A. et al. A comparison between gravity wave momentum fluxes in observations and climate models. J. Climate 26, 6383–6405 (2013).",
        "31. Anthes, R. A. Exploring Earth's atmosphere with radio occultation: contributions to weather, climate and space weather. Atmos. Meas. Tech. 4, 1077–1103 (2011).",
        "32. Steiner, A. K. et al. Quantification of structural uncertainty in climate data records from GPS radio occultation. Atmos. Chem. Phys. 13, 1469–1484 (2013).",
        "33. Ho, S.-P. et al. The COSMIC/FORMOSAT-3 radio occultation mission after 12 years: accomplishments, remaining challenges, and potential impacts of COSMIC-2. Bull. Am. Meteorol. Soc. 101, E1107–E1136 (2020).",
        "34. Schreiner, W. S. et al. COSMIC-2 radio occultation constellation: first results. Geophys. Res. Lett. 47, e2019GL086841 (2020).",
        "35. Alexander, M. J. & Grimsdell, A. W. Global estimates of gravity wave momentum flux from High Resolution Dynamics Limb Sounder observations. J. Geophys. Res. Atmos. 118, 6988–7007 (2013).",
        "36. Faber, A., Llamedo, P., Schmidt, T., de la Torre, A. & Wickert, J. On the determination of gravity wave momentum flux from GPS radio occultation data. Atmos. Meas. Tech. 6, 3169–3180 (2013).",
        "37. Ern, M. et al. Satellite observations of middle atmosphere gravity wave absolute momentum flux and of its vertical gradient during recent stratospheric warmings. Atmos. Chem. Phys. 16, 9983–10019 (2016).",
        "38. Wright, C. J. et al. Exploring gravity wave characteristics in 3-D using a novel S-transform technique on AIRS satellite data. Atmos. Chem. Phys. 17, 8553–8575 (2017).",
        "39. Dunkerton, T. J. The role of gravity waves in the quasi-biennial oscillation. J. Geophys. Res. 102, 26053–26076 (1997).",
        "40. Garfinkel, C. I. & Hartmann, D. L. Different ENSO teleconnections and their effects on the stratospheric polar vortex. J. Geophys. Res. 113, D18114 (2008).",
        "41. Bushell, A. C. et al. Evaluation of the quasi-biennial oscillation in global climate models for the SPARC QBO-initiative. Q. J. R. Meteorol. Soc. 148, 1459–1489 (2022).",
        "42. Gerber, E. P. et al. The Dynamics and Variability Model Intercomparison Project (DynVarMIP) for CMIP6: assessing the stratosphere–troposphere system. Geosci. Model Dev. 9, 3413–3425 (2016).",
        "43. Back, S. Y. et al. MJO diversity in CMIP6 models and its relationship to mean state biases. J. Climate 37, 4835–4850 (2024).",
        "44. Domeisen, D. I. V. et al. The role of the stratosphere in subseasonal to seasonal prediction: 1. Predictability of the stratosphere. J. Geophys. Res. Atmos. 125, e2019JD030920 (2020).",
        "45. Butler, A. H. et al. The Climate-system Historical Forecast Project: do stratosphere-resolving models make better seasonal climate predictions in boreal winter? Q. J. R. Meteorol. Soc. 142, 1413–1427 (2016).",
        "46. Ern, M. et al. A global view of gravity wave hotspots located by the SABER and HIRDLS satellite instruments. Atmos. Chem. Phys. 18, 12039–12062 (2018).",
        "47. Preusse, P. et al. Gravity wave momentum flux in the lower stratosphere over Europe and Canada from Rayleigh lidar data. Atmos. Chem. Phys. 6, 2441–2458 (2006).",
        "48. Cohen, N. Y., Gerber, E. P. & Bühler, O. What drives the Brewer-Dobson circulation? J. Atmos. Sci. 71, 3837–3855 (2014)."
    ]
    
    ref_heading_element = ref_heading._element
    parent = ref_heading_element.getparent()
    
    for ref in enhanced_references:
        ref_para = doc.add_paragraph()
        ref_para.add_run(ref)
        parent.insert(parent.index(ref_heading_element) + len(enhanced_references) - enhanced_references.index(ref), ref_para._element)
    
    return doc

if __name__ == "__main__":
    # Load the existing document
    doc = Document('Harmonized_LLNL_MJO_Proposal_E3SM_Validation_Final.docx')
    
    # Update background section
    doc = update_background_section(doc)
    
    # Update references section
    doc = update_references_section(doc)
    
    # Save the updated document
    doc.save('Harmonized_LLNL_MJO_Proposal_Enhanced_Background.docx')
    print("Enhanced background section and references updated successfully!")
