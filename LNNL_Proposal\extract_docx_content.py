#!/usr/bin/env python3
"""
Script to extract content from MJO_proposal_TTA_Chron_Cor_TTA.docx
and analyze its structure for LaTeX template creation.
"""

import os
import re
from docx import Document
from docx.document import Document as DocumentType
from docx.oxml.table import CT_Tbl
from docx.oxml.text.paragraph import CT_P
from docx.table import _Cell, Table
from docx.text.paragraph import Paragraph

def extract_docx_content_with_figures(docx_path):
    """Extract text content, figures, and equations from a DOCX file."""
    try:
        doc = Document(docx_path)
        content = []
        figures = []
        equations = []
        tables = []

        print(f"Extracting content from: {docx_path}")
        print(f"Number of paragraphs: {len(doc.paragraphs)}")

        # Extract paragraphs and identify special content
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()

            # Check for equations (look for mathematical symbols or equation-like patterns)
            if any(symbol in text for symbol in ['=', '∂', '∇', '∫', '∑', '±', '≈', '≤', '≥', '∞']):
                equations.append(f"Equation {len(equations)+1} (Para {i+1}): {text}")

            # Check for figure references
            if re.search(r'[Ff]igure\s*\d+|[Ff]ig\.\s*\d+', text):
                figures.append(f"Figure reference (Para {i+1}): {text}")

            if text:  # Only include non-empty paragraphs
                content.append(f"Para {i+1}: {text}")

        # Extract tables
        for i, table in enumerate(doc.tables):
            table_content = []
            for row in table.rows:
                row_content = []
                for cell in row.cells:
                    row_content.append(cell.text.strip())
                table_content.append(row_content)
            tables.append(f"Table {i+1}: {table_content}")

        # Check for embedded objects (images, equations)
        for rel in doc.part.rels.values():
            if "image" in rel.target_ref:
                figures.append(f"Embedded image: {rel.target_ref}")

        return content, figures, equations, tables

    except Exception as e:
        print(f"Error reading DOCX file: {e}")
        return None, None, None, None

def extract_docx_content(docx_path):
    """Extract text content from a DOCX file."""
    try:
        doc = Document(docx_path)
        content = []

        print(f"Extracting content from: {docx_path}")
        print(f"Number of paragraphs: {len(doc.paragraphs)}")

        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            if text:  # Only include non-empty paragraphs
                content.append(f"Para {i+1}: {text}")

        return content

    except Exception as e:
        print(f"Error reading DOCX file: {e}")
        return None

def analyze_citations(content):
    """Analyze citation patterns in the content."""
    citation_patterns = []
    superscript_pattern = r'\^(\d+(?:,\s*\d+)*)'  # Pattern for superscript citations
    bracket_pattern = r'\[(\d+(?:,\s*\d+)*)\]'    # Pattern for bracket citations
    
    for line in content:
        # Look for superscript citations
        superscript_matches = re.findall(superscript_pattern, line)
        if superscript_matches:
            citation_patterns.append(f"Superscript citations found: {superscript_matches}")
        
        # Look for bracket citations
        bracket_matches = re.findall(bracket_pattern, line)
        if bracket_matches:
            citation_patterns.append(f"Bracket citations found: {bracket_matches}")
    
    return citation_patterns

def main():
    docx_file = "MJO_proposal_TTA_Chron_Cor_TTA.docx"

    if not os.path.exists(docx_file):
        print(f"Error: File {docx_file} not found in current directory")
        return

    # Extract content with figures and equations
    content, figures, equations, tables = extract_docx_content_with_figures(docx_file)

    if content:
        # Save extracted content to text file
        with open("extracted_content_complete.txt", "w", encoding="utf-8") as f:
            f.write("=== MAIN CONTENT ===\n")
            for line in content:
                f.write(line + "\n")

            f.write("\n=== FIGURES ===\n")
            for figure in figures:
                f.write(figure + "\n")

            f.write("\n=== EQUATIONS ===\n")
            for equation in equations:
                f.write(equation + "\n")

            f.write("\n=== TABLES ===\n")
            for table in tables:
                f.write(table + "\n")

        print(f"\nComplete content extracted and saved to extracted_content_complete.txt")
        print(f"Total paragraphs with content: {len(content)}")
        print(f"Figures found: {len(figures)}")
        print(f"Equations found: {len(equations)}")
        print(f"Tables found: {len(tables)}")

        # Analyze citations
        citations = analyze_citations(content)
        if citations:
            print(f"\nCitation patterns found:")
            for citation in citations[:10]:  # Show first 10 citation patterns
                print(f"  {citation}")

        # Show figures and equations
        if figures:
            print(f"\nFigures found:")
            for figure in figures:
                print(f"  {figure}")

        if equations:
            print(f"\nEquations found:")
            for equation in equations:
                print(f"  {equation}")

        if tables:
            print(f"\nTables found:")
            for table in tables[:3]:  # Show first 3 tables
                print(f"  {table}")

    else:
        print("Failed to extract content from the document")

if __name__ == "__main__":
    main()
