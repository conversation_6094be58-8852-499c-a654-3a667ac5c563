This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: MJO_proposal_restructured.aux
Reallocating 'name_of_file' (item size: 1) to 9 items.
The style file: unsrtnat.bst
Reallocating 'name_of_file' (item size: 1) to 32 items.
Database file #1: MJO_proposal_references_reduced.bib
You've used 21 entries,
            2481 wiz_defined-function locations,
            704 strings with 10467 characters,
and the built_in function-call counts, 10717 in all, are:
= -- 786
> -- 865
< -- 3
+ -- 311
- -- 268
* -- 1077
:= -- 1858
add.period$ -- 85
call.type$ -- 21
change.case$ -- 42
chr.to.int$ -- 20
cite$ -- 21
duplicate$ -- 395
empty$ -- 796
format.name$ -- 293
if$ -- 2139
int.to.chr$ -- 2
int.to.str$ -- 22
missing$ -- 21
newline$ -- 135
num.names$ -- 63
pop$ -- 127
preamble$ -- 1
purify$ -- 21
quote$ -- 0
skip$ -- 216
stack$ -- 0
substring$ -- 545
swap$ -- 44
text.length$ -- 0
text.prefix$ -- 0
top$ -- 0
type$ -- 147
warning$ -- 0
while$ -- 70
width$ -- 0
write$ -- 323
