#!/usr/bin/env python3
"""
Harmonized LLNL Lawrence Fellowship Proposal Generator - Part 2
Continues with remaining sections focusing on observational analysis and E3SM validation
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH

def add_superscript_reference(paragraph, ref_num):
    """Add superscript reference number to paragraph"""
    run = paragraph.add_run(str(ref_num))
    run.font.superscript = True
    run.font.size = Pt(8)

def add_methodology_section(doc):
    """Add the Methodology section"""
    
    # Methodology
    method_heading = doc.add_heading('Methodology', level=1)
    
    # Data Acquisition and Processing
    data_heading = doc.add_heading('Data Acquisition and Processing', level=2)
    
    data_intro = doc.add_paragraph()
    data_intro.add_run("The methodology integrates cutting-edge satellite data processing, advanced atmospheric dynamics diagnostics, and robust statistical analysis applied to the extensive multi-mission RO dataset (2006-2024) and E3SM model output. This comprehensive approach leverages LLNL's world-class computational resources and expertise in large-scale data analysis to provide critical observational constraints for E3SM model evaluation.")
    
    # Data sources
    data_sources = [
        "• Radio Occultation Data: Level 2 'dry' temperature profiles from COSMIC-1/2, MetOp, Spire, and other missions (2006-2024) totaling >6 million profiles globally. Data acquired from CDAAC, EUMETSAT, and commercial providers with comprehensive quality control and inter-mission calibration.",
        "• E3SM Model Output: High-resolution E3SM-EAM simulations including 3D temperature fields, parameterized gravity wave momentum fluxes, convective heating rates, and atmospheric circulation patterns. Analysis will focus on E3SM's representation of MJO-related processes and gravity wave parameterizations.",
        "• Reanalysis Data: ERA5 hourly/daily/monthly fields (winds, temperature, geopotential height, humidity, surface pressure) at 0.25° resolution for background state characterization and GW filtering analysis.",
        "• Climate Indices: Real-time Multivariate MJO Index (RMM1, RMM2), QBO index (ERA5 U50, U30), ENSO indices (Niño3.4, SOI), solar activity indices (F10.7 cm flux) for comprehensive climate mode analysis.",
        "• Satellite Observations: Outgoing Longwave Radiation (OLR) data from NOAA, precipitation data from TRMM/GPM, and complementary GW observations from TIMED/SABER and Aura/MLS for validation and cross-comparison.",
        "• CMIP6/CMIP7 Model Data: Daily outputs from multiple Earth system models including 3D temperature, winds, and parameterized GW momentum fluxes for inter-model comparison and E3SM benchmarking."
    ]
    
    for source in data_sources:
        source_para = doc.add_paragraph()
        source_para.add_run(source)
    
    return doc

def add_gw_analysis_section(doc):
    """Add the Advanced GW Parameter Extraction section"""
    
    # Advanced GW Parameter Extraction
    gw_heading = doc.add_heading('Advanced GW Parameter Extraction and E3SM Comparison', level=2)
    
    gw_intro = doc.add_paragraph()
    gw_intro.add_run("The GW parameter extraction methodology represents a significant advancement over traditional approaches, incorporating state-of-the-art signal processing techniques, comprehensive uncertainty quantification, and innovative comparison methods between observations and E3SM model output. For each temperature profile T(z), we will apply a sophisticated multi-step analysis framework:")
    
    # Analysis steps
    steps = [
        "(i) Background Temperature Estimation: Apply advanced wavelet decomposition using Daubechies and Morlet wavelets to separate background temperature T̄(z) from perturbations T'(z) = T(z) - T̄(z). Implement adaptive filtering techniques that account for varying atmospheric conditions and altitude-dependent scale separation, with identical methods applied to E3SM output for direct comparison.",
        
        "(ii) Atmospheric Stability Analysis: Calculate buoyancy frequency N²(z) = (g/T̄)(dT̄/dz + g/cₚ) with careful treatment of atmospheric stability, including identification of critical levels, turning points, and regions of wave breaking. Compare E3SM's representation of atmospheric stability with observations.",
        
        "(iii) GW Potential Energy Calculation: Compute GW potential energy per unit mass Eₚₘ(z) = ½(g/N)²(T'/T̄)² with comprehensive uncertainty quantification including instrumental noise, retrieval errors, and methodological uncertainties. Evaluate E3SM's parameterized GW energy against observational estimates.",
        
        "(iv) E3SM Model-Observation Integration: Extract parameterized GW momentum fluxes from E3SM-EAM output, including contributions from orographic drag schemes, convective gravity wave sources, and non-orographic wave drag. Develop innovative techniques for direct comparison between E3SM's parameterized GW effects and observational estimates, accounting for differences in spatial and temporal resolution.",
        
        "(v) Momentum Flux Estimation: Estimate GW momentum flux components using established relationships involving background density, horizontal wavenumbers, vertical wavenumber, and wave potential energy. Apply sensitivity analysis to assess the impact of different estimation methods and compare with E3SM's parameterized momentum flux distributions."
    ]
    
    for step in steps:
        step_para = doc.add_paragraph()
        step_para.add_run(step)
    
    return doc

def add_mjo_analysis_section(doc):
    """Add the MJO Impact Analysis section"""
    
    # MJO Impact Analysis
    mjo_heading = doc.add_heading('MJO Impact Analysis and E3SM Evaluation Framework', level=2)
    
    mjo_intro = doc.add_paragraph()
    mjo_intro.add_run("The research employs a comprehensive framework specifically designed to characterize MJO-gravity wave interactions in observations and evaluate E3SM's representation of these processes:")
    
    # Framework components
    components = [
        "• Global MJO-GW Climatology Development: Utilize validated RO-derived GW parameters to develop comprehensive global climatology of GW activity modulated by MJO, categorizing events by propagation types (standing, jumping, slow/fast-propagating) and performing composite analysis for each type. Create parallel climatology using E3SM output to enable direct comparison.",
        
        "• E3SM Performance Assessment: Systematic evaluation of E3SM's representation of MJO-GW relationships using observational benchmarks, including analysis of convective parameterizations, gravity wave drag schemes, and stratospheric circulation patterns. Assess model skill in capturing observed MJO propagation characteristics and their impact on gravity wave generation.",
        
        "• Multi-scale Interaction Analysis: Investigate interactive modulation by QBO and ENSO in both observations and E3SM simulations, providing insights into the model's representation of multi-scale atmospheric coupling processes. Evaluate E3SM's ability to capture the complex feedbacks between different climate modes.",
        
        "• Statistical Validation Framework: Apply established metrics for model evaluation, including correlation analysis, bias assessment, and skill scores to quantify E3SM's performance relative to observations. Develop new metrics specifically designed for evaluating gravity wave parameterizations in climate models.",
        
        "• CMIP6/CMIP7 Benchmarking: Systematic comparison of E3SM performance against other leading Earth system models to establish E3SM's competitive advantages and identify areas for continued development. Provide context for E3SM's performance within the broader climate modeling community."
    ]
    
    for component in components:
        comp_para = doc.add_paragraph()
        comp_para.add_run(component)
    
    return doc

def add_relevance_section(doc):
    """Add the Relevance to LLNL section"""
    
    # Relevance to LLNL
    rel_heading = doc.add_heading('Relevance to LLNL\'s Research and Strategic Plan', level=1)
    
    rel_intro = doc.add_paragraph()
    rel_intro.add_run("This research directly aligns with LLNL's strategic priorities and mission areas, contributing to the Laboratory's leadership in Earth system science, climate modeling, computational science, and national security applications. The project provides critical observational validation for LLNL's flagship E3SM climate model while advancing fundamental understanding of atmospheric processes.")
    
    # E3SM Validation and Development
    e3sm_dev_heading = doc.add_heading('E3SM Model Validation and Performance Assessment', level=2)
    
    e3sm_dev_para = doc.add_paragraph()
    e3sm_dev_para.add_run("The research provides comprehensive observational validation for E3SM's atmospheric physics parameterizations, specifically targeting gravity wave processes that are fundamental to stratosphere-troposphere coupling. The systematic evaluation of E3SM's performance in representing MJO-gravity wave interactions will provide critical insights into the model's capabilities and limitations, supporting LLNL's leadership in climate science and Earth system modeling. The project addresses key E3SM evaluation priorities including assessment of convective parameterizations, gravity wave physics, and representation of multi-scale atmospheric interactions.")
    
    # Computational Science
    comp_sci_heading = doc.add_heading('Computational Science and High-Performance Computing', level=2)
    
    comp_sci_para = doc.add_paragraph()
    comp_sci_para.add_run("The comprehensive analysis of multi-decade, global RO datasets and E3SM model output leverages LLNL's world-class computational resources and expertise in large-scale data analysis. The development of advanced statistical methods for model-observation comparison contributes to LLNL's computational science capabilities, while the innovative uncertainty quantification methods advance the Laboratory's expertise in model validation and verification. The project utilizes LLNL's supercomputing capabilities for processing and analyzing large climate datasets.")
    
    # National Security Applications
    ns_heading = doc.add_heading('National Security and Energy Applications', level=2)
    
    ns_para = doc.add_paragraph()
    ns_para.add_run("The research has direct implications for national security through improved understanding and validation of atmospheric processes on subseasonal-to-seasonal timescales, supporting weather-related impacts assessment for military operations, energy infrastructure vulnerability analysis, and climate-related security threat evaluation. Enhanced understanding of E3SM's predictive capabilities resulting from this research supports critical decision-making for national security applications, emergency preparedness, and strategic planning. The project contributes to understanding atmospheric processes that affect renewable energy resources and energy infrastructure resilience, directly relevant to LLNL's energy security mission.")
    
    return doc

def add_innovation_section(doc):
    """Add the Innovation and Expected Impact section"""
    
    # Innovation and Impact
    innov_heading = doc.add_heading('Innovation and Expected Impact', level=1)
    
    innov_intro = doc.add_paragraph()
    innov_intro.add_run("This research represents a significant advancement in our understanding of stratosphere-troposphere coupling with direct applications to E3SM model validation and climate science:")
    
    # Scientific Innovation
    sci_innov_heading = doc.add_heading('Scientific Innovation', level=2)
    
    sci_innovations = [
        "• First comprehensive global characterization of MJO-modulated stratospheric GW activity using the complete multi-mission RO dataset with systematic E3SM model validation",
        "• Development of advanced momentum flux estimation techniques with comprehensive uncertainty quantification applicable to both observations and climate model output",
        "• Novel analysis of interactive climate mode effects (QBO, ENSO) on MJO-GW relationships with specific focus on E3SM's representation of these multi-scale interactions",
        "• Pioneering integration of global RO observations with E3SM model evaluation to provide critical observational constraints for climate model assessment"
    ]
    
    for innovation in sci_innovations:
        innov_para = doc.add_paragraph()
        innov_para.add_run(innovation)
    
    # E3SM Model Assessment
    e3sm_assess_heading = doc.add_heading('E3SM Model Assessment and Validation', level=2)
    
    e3sm_advances = [
        "• Comprehensive evaluation of E3SM's gravity wave parameterizations using global observational constraints, providing critical insights into model performance",
        "• Systematic assessment of E3SM's representation of MJO-related processes and their impact on stratospheric circulation",
        "• Advanced validation methodologies for climate model evaluation using global satellite observations, establishing new standards for model assessment",
        "• Quantitative benchmarking of E3SM's performance relative to other leading climate models in representing MJO-gravity wave interactions"
    ]
    
    for advance in e3sm_advances:
        adv_para = doc.add_paragraph()
        adv_para.add_run(advance)
    
    # LLNL Mission Impact
    mission_heading = doc.add_heading('LLNL Mission Impact', level=2)
    
    mission_impacts = [
        "• Critical validation data for E3SM model development and performance assessment, supporting LLNL's leadership in Earth system modeling",
        "• Enhanced understanding of atmospheric prediction capabilities relevant to national security and energy applications",
        "• Strengthened LLNL position in international climate modeling community through cutting-edge observational analysis",
        "• Advanced computational science capabilities through innovative large-scale data analysis and model validation techniques"
    ]
    
    for impact in mission_impacts:
        impact_para = doc.add_paragraph()
        impact_para.add_run(impact)
    
    return doc

if __name__ == "__main__":
    # Load the existing document
    doc = Document('Harmonized_LLNL_MJO_Proposal_E3SM_Validation.docx')
    
    # Add remaining sections
    doc = add_methodology_section(doc)
    doc = add_gw_analysis_section(doc)
    doc = add_mjo_analysis_section(doc)
    doc = add_relevance_section(doc)
    doc = add_innovation_section(doc)
    
    # Save the completed document
    doc.save('Harmonized_LLNL_MJO_Proposal_E3SM_Validation_Complete.docx')
    print("Complete harmonized LLNL proposal document created successfully!")
