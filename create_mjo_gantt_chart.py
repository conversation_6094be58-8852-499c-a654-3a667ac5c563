#!/usr/bin/env python3
"""
Create a Gantt chart for the MJO project based on the work packages from the proposal
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
from datetime import datetime, timedelta
import pandas as pd

def create_mjo_gantt_chart():
    """Create a professional Gantt chart for the MJO project"""
    
    # Define the work packages and their timelines (in months)
    work_packages = [
        {
            'name': 'WP0: Project Management & Setup',
            'start': 1,
            'duration': 6,
            'color': '#FF6B6B',
            'description': 'Project initiation, data acquisition, setup'
        },
        {
            'name': 'WP1: Validation and GW Parameter Climatology',
            'start': 3,
            'duration': 10,
            'color': '#4ECDC4',
            'description': 'RO validation, GW parameter extraction'
        },
        {
            'name': 'WP2: MJO-Modulated GW Activity Analysis',
            'start': 10,
            'duration': 9,
            'color': '#45B7D1',
            'description': 'Global climatology, MJO categorization'
        },
        {
            'name': 'WP3: Climate Model Evaluation',
            'start': 16,
            'duration': 7,
            'color': '#96CEB4',
            'description': 'CMIP6/7 model assessment'
        },
        {
            'name': 'WP4: Dissemination, Exploitation & Reporting',
            'start': 1,
            'duration': 24,
            'color': '#FFEAA7',
            'description': 'Ongoing publications and reporting'
        }
    ]
    
    # Define key deliverables
    deliverables = [
        {'name': 'D0.1: Career Development Plan', 'month': 3, 'color': '#E17055'},
        {'name': 'D0.2: Data Management Plan', 'month': 6, 'color': '#E17055'},
        {'name': 'D1.1: Validation Report & GW Climatology', 'month': 12, 'color': '#00B894'},
        {'name': 'D2.1: MJO-GW Climatology Report', 'month': 18, 'color': '#0984E3'},
        {'name': 'D3.1: Model Evaluation Report', 'month': 22, 'color': '#00CEC9'},
        {'name': 'D4.1: Final Project Report', 'month': 24, 'color': '#FDCB6E'}
    ]
    
    # Create the figure and axis
    fig, ax = plt.subplots(figsize=(16, 10))
    
    # Set up the chart
    y_positions = np.arange(len(work_packages))
    
    # Plot work packages as horizontal bars
    for i, wp in enumerate(work_packages):
        # Main work package bar
        bar = ax.barh(i, wp['duration'], left=wp['start']-1, height=0.6, 
                     color=wp['color'], alpha=0.8, edgecolor='white', linewidth=1)
        
        # Add work package labels
        ax.text(wp['start'] + wp['duration']/2 - 0.5, i, wp['name'], 
                ha='center', va='center', fontweight='bold', fontsize=10, color='white')
    
    # Add deliverable markers
    for deliv in deliverables:
        # Find which WP this deliverable belongs to based on timing
        wp_index = 0
        for i, wp in enumerate(work_packages):
            if wp['start'] <= deliv['month'] <= wp['start'] + wp['duration'] - 1:
                if wp['name'] != 'WP4: Dissemination, Exploitation & Reporting':
                    wp_index = i
                    break
        
        # Add diamond marker for deliverable
        ax.scatter(deliv['month'], wp_index, s=200, marker='D', 
                  color=deliv['color'], edgecolor='black', linewidth=2, zorder=10)
        
        # Add deliverable label
        ax.annotate(deliv['name'], (deliv['month'], wp_index), 
                   xytext=(10, 20), textcoords='offset points',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor=deliv['color'], alpha=0.7),
                   arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0.1'),
                   fontsize=8, fontweight='bold')
    
    # Customize the chart
    ax.set_xlim(0, 25)
    ax.set_ylim(-0.5, len(work_packages) - 0.5)
    
    # Set labels and title
    ax.set_xlabel('Project Timeline (Months)', fontsize=14, fontweight='bold')
    ax.set_ylabel('Work Packages', fontsize=14, fontweight='bold')
    ax.set_title('MJO Stratospheric Gravity Waves Project - 24 Month Timeline\n' +
                'Observational Analysis and E3SM Model Validation', 
                fontsize=16, fontweight='bold', pad=20)
    
    # Set y-axis labels
    ax.set_yticks(y_positions)
    ax.set_yticklabels([wp['name'] for wp in work_packages])
    
    # Set x-axis ticks for months
    months = np.arange(1, 25)
    ax.set_xticks(months)
    ax.set_xticklabels([f'M{m}' for m in months], rotation=45)
    
    # Add grid
    ax.grid(True, axis='x', alpha=0.3, linestyle='--')
    ax.set_axisbelow(True)
    
    # Add quarter markers
    quarters = [6, 12, 18, 24]
    for q in quarters:
        ax.axvline(x=q, color='red', linestyle='--', alpha=0.5, linewidth=2)
        ax.text(q, len(work_packages), f'Q{q//6}', ha='center', va='bottom', 
                fontweight='bold', color='red', fontsize=12)
    
    # Add legend for work packages
    legend_elements = []
    for wp in work_packages:
        legend_elements.append(patches.Patch(color=wp['color'], label=wp['description']))
    
    ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1.0, 1.0),
             title='Work Package Descriptions', title_fontsize=12, fontsize=10)
    
    # Adjust layout
    plt.tight_layout()
    
    # Save the chart
    plt.savefig('MJO_Project_Gantt_Chart.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.savefig('MJO_Project_Gantt_Chart.pdf', bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print("Gantt chart saved as 'MJO_Project_Gantt_Chart.png' and 'MJO_Project_Gantt_Chart.pdf'")
    
    return fig, ax

def create_detailed_timeline_table():
    """Create a detailed timeline table to accompany the Gantt chart"""
    
    # Define detailed activities
    activities = [
        # WP0 Activities
        {'WP': 'WP0', 'Activity': 'Project initiation and setup', 'Start': 1, 'End': 2},
        {'WP': 'WP0', 'Activity': 'Data acquisition (RO, ERA5, CMIP)', 'Start': 1, 'End': 4},
        {'WP': 'WP0', 'Activity': 'Analysis environment setup', 'Start': 2, 'End': 3},
        {'WP': 'WP0', 'Activity': 'Career Development Plan (D0.1)', 'Start': 2, 'End': 3},
        {'WP': 'WP0', 'Activity': 'Data Management Plan (D0.2)', 'Start': 4, 'End': 6},
        
        # WP1 Activities
        {'WP': 'WP1', 'Activity': 'RO data preprocessing and quality control', 'Start': 3, 'End': 5},
        {'WP': 'WP1', 'Activity': 'GW parameter extraction methodology', 'Start': 4, 'End': 7},
        {'WP': 'WP1', 'Activity': 'Validation with TIMED/SABER and Aura/MLS', 'Start': 6, 'End': 9},
        {'WP': 'WP1', 'Activity': 'Case study analysis (August 2019 event)', 'Start': 7, 'End': 9},
        {'WP': 'WP1', 'Activity': 'Global GW climatology development', 'Start': 9, 'End': 12},
        {'WP': 'WP1', 'Activity': 'Validation report preparation (D1.1)', 'Start': 11, 'End': 12},
        
        # WP2 Activities
        {'WP': 'WP2', 'Activity': 'MJO event categorization', 'Start': 10, 'End': 12},
        {'WP': 'WP2', 'Activity': 'MJO-GW composite analysis', 'Start': 12, 'End': 15},
        {'WP': 'WP2', 'Activity': 'QBO and ENSO interaction analysis', 'Start': 14, 'End': 17},
        {'WP': 'WP2', 'Activity': 'Global climatology synthesis', 'Start': 16, 'End': 18},
        {'WP': 'WP2', 'Activity': 'MJO-GW climatology report (D2.1)', 'Start': 17, 'End': 18},
        
        # WP3 Activities
        {'WP': 'WP3', 'Activity': 'CMIP6/7 data acquisition and processing', 'Start': 16, 'End': 18},
        {'WP': 'WP3', 'Activity': 'Model-observation comparison framework', 'Start': 17, 'End': 19},
        {'WP': 'WP3', 'Activity': 'Systematic bias identification', 'Start': 19, 'End': 21},
        {'WP': 'WP3', 'Activity': 'Model evaluation metrics application', 'Start': 20, 'End': 22},
        {'WP': 'WP3', 'Activity': 'Model evaluation report (D3.1)', 'Start': 21, 'End': 22},
        
        # WP4 Activities
        {'WP': 'WP4', 'Activity': 'Conference presentations (ongoing)', 'Start': 6, 'End': 24},
        {'WP': 'WP4', 'Activity': 'First manuscript preparation', 'Start': 12, 'End': 15},
        {'WP': 'WP4', 'Activity': 'Second manuscript preparation', 'Start': 18, 'End': 21},
        {'WP': 'WP4', 'Activity': 'Third manuscript preparation', 'Start': 22, 'End': 24},
        {'WP': 'WP4', 'Activity': 'Data archiving and documentation', 'Start': 20, 'End': 24},
        {'WP': 'WP4', 'Activity': 'Final project report (D4.1)', 'Start': 23, 'End': 24},
    ]
    
    # Create DataFrame
    df = pd.DataFrame(activities)
    df['Duration'] = df['End'] - df['Start'] + 1
    
    # Save to CSV
    df.to_csv('MJO_Project_Detailed_Timeline.csv', index=False)
    print("Detailed timeline saved as 'MJO_Project_Detailed_Timeline.csv'")
    
    return df

if __name__ == "__main__":
    # Create the Gantt chart
    fig, ax = create_mjo_gantt_chart()
    
    # Create detailed timeline table
    timeline_df = create_detailed_timeline_table()
    
    # Display the chart
    plt.show()
    
    print("\nProject Timeline Summary:")
    print("=" * 50)
    print("Total Duration: 24 months")
    print("Work Packages: 5 (WP0-WP4)")
    print("Key Deliverables: 6")
    print("Expected Publications: 2-3 peer-reviewed articles")
    print("=" * 50)
