#!/usr/bin/env python3
"""
Harmonized LLNL Lawrence Fellowship Proposal Generator
Combines elements from both proposals with focus on observational analysis and E3SM validation
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE

def add_superscript_reference(paragraph, ref_num):
    """Add superscript reference number to paragraph"""
    run = paragraph.add_run(str(ref_num))
    run.font.superscript = True
    run.font.size = Pt(8)

def create_harmonized_proposal():
    """Create the harmonized LLNL proposal document"""
    
    # Create document
    doc = Document()
    
    # Set document margins
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(0.8)
        section.bottom_margin = Inches(0.8)
        section.left_margin = Inches(0.8)
        section.right_margin = Inches(0.8)
    
    # Configure styles
    styles = doc.styles
    
    # Normal style
    normal_style = styles['Normal']
    normal_font = normal_style.font
    normal_font.name = 'Times New Roman'
    normal_font.size = Pt(10)
    
    # Paragraph formatting
    normal_paragraph = normal_style.paragraph_format
    normal_paragraph.line_spacing = 1.15
    normal_paragraph.space_after = Pt(6)
    
    # Heading styles
    heading1_style = styles['Heading 1']
    heading1_font = heading1_style.font
    heading1_font.name = 'Times New Roman'
    heading1_font.size = Pt(12)
    heading1_font.bold = True
    
    heading2_style = styles['Heading 2']
    heading2_font = heading2_style.font
    heading2_font.name = 'Times New Roman'
    heading2_font.size = Pt(11)
    heading2_font.bold = True
    
    # Title
    title = doc.add_heading('', level=0)
    title_run = title.runs[0] if title.runs else title.add_run()
    title_run.text = 'Modulation of Stratospheric Gravity Waves by the Madden-Julian Oscillation: Observational Evidence from Radio Occultation and Validation with E3SM Climate Model'
    title_run.font.name = 'Times New Roman'
    title_run.font.size = Pt(14)
    title_run.font.bold = True
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Author info
    author_para = doc.add_paragraph()
    author_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    author_run = author_para.add_run('Dr. Toyese Tunde Ayorinde\nLawrence Fellowship Research Proposal\nLawrence Livermore National Laboratory\nAtmospheric, Earth, and Energy Division')
    author_run.font.name = 'Times New Roman'
    author_run.font.size = Pt(11)
    
    # Add spacing
    doc.add_paragraph()
    
    return doc

def add_abstract_section(doc):
    """Add the Abstract section"""
    
    # Abstract
    abstract_heading = doc.add_heading('Abstract', level=1)
    
    abstract_para = doc.add_paragraph()
    abstract_para.add_run("Understanding the coupling between the tropical troposphere and the global stratosphere through atmospheric gravity waves (GWs) is crucial for improving subseasonal-to-seasonal forecasts and climate models. The Madden-Julian Oscillation (MJO), as the primary driver of tropical intraseasonal variability, modulates tropical GW activity, yet the representation of this modulation in climate models remains inadequate, hindering predictive skill on subseasonal-to-seasonal timescales. This research leverages Global Navigation Satellite System Radio Occultation (RO) data to deliver a comprehensive, observationally-derived global characterization of stratospheric GWs modulated by the MJO, with specific validation using LLNL's E3SM climate model products. The project pursues three objectives: (1) Validation of RO-derived GW parameters against independent satellite measurements and E3SM model output, providing reliable estimates of GW potential energy and momentum flux; (2) Quantification of the MJO's modulation of GW activity, producing a novel climatology composited by MJO propagation types and comparing with E3SM representations; (3) Comprehensive evaluation of how E3SM and other state-of-the-art climate models represent MJO-GW interactions, using observational climatology as a benchmark to identify systematic biases. This research will yield a benchmark global climatology of MJO-modulated GW activity and provide critical observational constraints for validating E3SM's atmospheric physics, contributing to LLNL's leadership in Earth system modeling while advancing fundamental understanding of troposphere-stratosphere coupling mechanisms.")
    
    return doc

def add_background_section(doc):
    """Add the Enhanced Background and Motivation section"""

    # Background and Motivation
    bg_heading = doc.add_heading('Background and Motivation', level=1)

    # First paragraph - MJO fundamentals
    p1 = doc.add_paragraph()
    p1_text = "The Madden-Julian Oscillation (MJO) is the dominant mode of tropical intraseasonal variability, characterized by eastward-propagating large-scale coupled patterns of convection and circulation with periods of 30-90 days"
    p1.add_run(p1_text)
    add_superscript_reference(p1, 1)
    p1.add_run(",")
    add_superscript_reference(p1, 2)
    p1.add_run(". The MJO exhibits complex spatial structure with distinct convective and suppressed phases that modulate tropical precipitation, outgoing longwave radiation, and atmospheric heating profiles across the Indo-Pacific warm pool")
    add_superscript_reference(p1, 3)
    p1.add_run(",")
    add_superscript_reference(p1, 4)
    p1.add_run(". This oscillation exerts profound influence on global weather patterns through complex teleconnections, affecting storm tracks, cyclone activity, atmospheric rivers, and blocking events across the Northern Hemisphere")
    add_superscript_reference(p1, 5)
    p1.add_run(",")
    add_superscript_reference(p1, 6)
    p1.add_run(",")
    add_superscript_reference(p1, 7)
    p1.add_run(", with significant implications for subseasonal-to-seasonal prediction and climate variability")
    add_superscript_reference(p1, 8)
    p1.add_run(".")

    # Second paragraph - Gravity waves and stratosphere-troposphere coupling
    p2 = doc.add_paragraph()
    p2.add_run("Atmospheric gravity waves (GWs) represent a critical mechanism for stratosphere-troposphere coupling, transporting momentum and energy from the troposphere to the stratosphere and mesosphere")
    add_superscript_reference(p2, 9)
    p2.add_run(",")
    add_superscript_reference(p2, 10)
    p2.add_run(". These waves are generated by various sources including topography, convection, jet-front systems, and spontaneous adjustment processes, with tropical convection being a dominant source of stratospheric GW activity")
    add_superscript_reference(p2, 11)
    p2.add_run(",")
    add_superscript_reference(p2, 12)
    p2.add_run(". The momentum deposition from breaking and dissipating GWs drives critical stratospheric circulations including the Brewer-Dobson Circulation (BDC) and the Quasi-Biennial Oscillation (QBO), which in turn influence tropospheric weather patterns, ozone distribution, and climate variability")
    add_superscript_reference(p2, 13)
    p2.add_run(",")
    add_superscript_reference(p2, 14)
    p2.add_run(",")
    add_superscript_reference(p2, 15)
    p2.add_run(".")

    # Third paragraph - MJO-GW interactions
    p3 = doc.add_paragraph()
    p3.add_run("The modulation of stratospheric GW activity by the MJO occurs through multiple pathways: (i) direct generation of convective GWs by MJO-related deep convection, with enhanced wave activity observed near convective centers")
    add_superscript_reference(p3, 16)
    p3.add_run(",")
    add_superscript_reference(p3, 17)
    p3.add_run("; (ii) modification of the background atmospheric state that affects GW propagation and filtering")
    add_superscript_reference(p3, 18)
    p3.add_run(",")
    add_superscript_reference(p3, 19)
    p3.add_run("; and (iii) interaction with other climate modes such as the QBO and ENSO that can amplify or suppress MJO-GW relationships")
    add_superscript_reference(p3, 20)
    p3.add_run(",")
    add_superscript_reference(p3, 21)
    p3.add_run(". Recent studies using satellite observations have documented significant correlations between MJO phase and stratospheric GW potential energy, with regional variations reflecting the complex interplay between convective sources and propagation conditions")
    add_superscript_reference(p3, 22)
    p3.add_run(",")
    add_superscript_reference(p3, 23)
    p3.add_run(".")

    # Fourth paragraph - E3SM context and challenges
    p4 = doc.add_paragraph()
    p4.add_run("LLNL's Energy Exascale Earth System Model (E3SM) represents a cutting-edge climate modeling framework designed to address critical Earth system science questions relevant to the Department of Energy's mission")
    add_superscript_reference(p4, 24)
    p4.add_run(". E3SM's atmospheric component (EAM) incorporates sophisticated parameterizations for atmospheric processes, including the Zhang-McFarlane convective scheme, orographic and non-orographic gravity wave drag parameterizations, and advanced cloud microphysics")
    add_superscript_reference(p4, 25)
    p4.add_run(",")
    add_superscript_reference(p4, 26)
    p4.add_run(". However, like other global climate models, E3SM faces significant challenges in accurately representing the complex interactions between the MJO and stratospheric gravity waves")
    add_superscript_reference(p4, 27)
    p4.add_run(",")
    add_superscript_reference(p4, 28)
    p4.add_run(". These challenges include limitations in convective parameterizations that affect GW generation, simplified gravity wave drag schemes that may not capture the full spectrum of wave-mean flow interactions, and insufficient representation of multi-scale coupling processes")
    add_superscript_reference(p4, 29)
    p4.add_run(",")
    add_superscript_reference(p4, 30)
    p4.add_run(".")

    # Fifth paragraph - Observational constraints and radio occultation
    p5 = doc.add_paragraph()
    p5.add_run("Radio Occultation (RO) observations provide unique advantages for studying stratospheric GWs, offering global coverage, high vertical resolution (~100-200 m), and long-term stability with minimal instrumental drift")
    add_superscript_reference(p5, 31)
    p5.add_run(",")
    add_superscript_reference(p5, 32)
    p5.add_run(". The multi-mission RO dataset spanning nearly two decades (2006-2024) from COSMIC-1/2, MetOp, Spire, and other missions represents an unprecedented observational resource for characterizing MJO-GW interactions globally")
    add_superscript_reference(p5, 33)
    p5.add_run(",")
    add_superscript_reference(p5, 34)
    p5.add_run(". Previous RO-based studies have demonstrated the capability to extract GW parameters including potential energy, vertical wavelengths, and momentum flux estimates, providing critical observational constraints for model validation")
    add_superscript_reference(p5, 35)
    p5.add_run(",")
    add_superscript_reference(p5, 36)
    p5.add_run(".")

    # Sixth paragraph - Scientific gaps and research needs
    p6 = doc.add_paragraph()
    p6.add_run("Current understanding of MJO-modulated GWs is constrained by several critical limitations: (i) Previous observational studies have often been limited in spatial coverage, temporal extent, or vertical resolution, preventing comprehensive global characterization")
    add_superscript_reference(p6, 37)
    p6.add_run(",")
    add_superscript_reference(p6, 38)
    p6.add_run("; (ii) The interactive modulation of MJO-GW relationships by background climate states (QBO, ENSO, solar cycle) remains poorly quantified on a global scale")
    add_superscript_reference(p6, 39)
    p6.add_run(",")
    add_superscript_reference(p6, 40)
    p6.add_run("; (iii) Systematic evaluation of climate model performance in representing MJO-GW interactions has been hindered by lack of comprehensive observational benchmarks")
    add_superscript_reference(p6, 41)
    p6.add_run(",")
    add_superscript_reference(p6, 42)
    p6.add_run("; and (iv) The representation of different MJO propagation characteristics (standing, jumping, slow/fast-propagating) and their differential impact on GW activity remains largely unexplored in both observations and models")
    add_superscript_reference(p6, 43)
    p6.add_run(".")

    # Seventh paragraph - Research opportunity and significance
    p7 = doc.add_paragraph()
    p7.add_run("This research addresses these critical knowledge gaps by leveraging the comprehensive multi-mission RO dataset to provide the first global, long-term observational characterization of MJO-modulated stratospheric GW activity, with systematic validation of E3SM model performance. The research is particularly timely given the growing recognition of the importance of stratosphere-troposphere coupling for subseasonal-to-seasonal prediction")
    add_superscript_reference(p7, 44)
    p7.add_run(",")
    add_superscript_reference(p7, 45)
    p7.add_run(", the increasing availability of high-quality satellite observations")
    add_superscript_reference(p7, 46)
    p7.add_run(", and the critical need for observational constraints to improve climate model representations of atmospheric wave processes")
    add_superscript_reference(p7, 47)
    p7.add_run(",")
    add_superscript_reference(p7, 48)
    p7.add_run(". The results will provide essential validation data for E3SM development while advancing fundamental understanding of multi-scale atmospheric interactions relevant to climate prediction, national security applications, and energy infrastructure planning.")

    return doc

def add_research_questions_section(doc):
    """Add the Research Questions section"""
    
    # Research Questions
    rq_heading = doc.add_heading('Research Questions and Objectives', level=1)
    
    # Introduction paragraph
    intro_para = doc.add_paragraph()
    intro_para.add_run("This research addresses fundamental questions about stratosphere-troposphere coupling through gravity waves modulated by the MJO, with direct applications to validating and understanding LLNL's E3SM model performance. The research leverages LLNL's expertise in large-scale data analysis, computational modeling, and Earth system science to provide critical observational constraints for climate model evaluation.")
    
    # Primary Research Question
    primary_heading = doc.add_heading('Primary Research Question:', level=2)
    
    primary_para = doc.add_paragraph()
    primary_para.add_run("How does the MJO modulate stratospheric gravity wave activity globally, what are the underlying physical mechanisms governing this relationship, and how well does E3SM represent these observed MJO-gravity wave interactions compared to other state-of-the-art climate models?")
    
    # Specific Scientific Questions
    specific_heading = doc.add_heading('Specific Scientific Questions:', level=2)
    
    questions = [
        "How do MJO-related changes in tropical convection translate into observable variations in stratospheric GW potential energy, momentum flux, and spectral characteristics across different geographical regions and seasons, and how do these compare with E3SM model output?",
        "What is the relative importance of source modulation (MJO convection) versus propagation filtering (MJO-modulated background state) in explaining observed MJO-GW relationships, and how well does E3SM capture these physical mechanisms?",
        "How do different MJO propagation types (standing, jumping, slow/fast-propagating) influence the global distribution of stratospheric GW activity, and what are the systematic differences between observations and E3SM representations?",
        "How do the QBO and ENSO interactively modulate MJO-GW relationships in observations, and how does E3SM's representation of these multi-scale interactions compare with observational evidence?",
        "What are the systematic biases in E3SM's gravity wave parameterizations when compared to comprehensive observational benchmarks, and how do these biases affect the model's representation of stratosphere-troposphere coupling?",
        "How can observational constraints from global RO datasets inform the evaluation and potential improvement of E3SM's atmospheric physics, particularly regarding MJO-related processes and their impact on stratospheric circulation?"
    ]
    
    for i, question in enumerate(questions, 1):
        q_para = doc.add_paragraph()
        q_para.add_run(f"{i}. {question}")
    
    return doc

def add_objectives_section(doc):
    """Add the Research Objectives section"""
    
    # Research Objectives
    obj_heading = doc.add_heading('Research Objectives', level=1)
    
    intro_para = doc.add_paragraph()
    intro_para.add_run("The research objectives are structured to address these questions systematically through three interconnected work packages that provide comprehensive observational analysis and E3SM model validation:")
    
    # Objective 1
    obj1_heading = doc.add_heading('Objective 1: Establish Observational Baseline and E3SM Validation Framework (Months 1-12)', level=2)
    
    obj1_para = doc.add_paragraph()
    obj1_para.add_run("Develop and validate robust methodologies for extracting GW parameters from RO temperature profiles through comprehensive process-based validation using focused case studies and statistical comparison with independent satellite measurements (TIMED/SABER, Aura/MLS)")
    add_superscript_reference(obj1_para, 10)
    obj1_para.add_run(",")
    add_superscript_reference(obj1_para, 11)
    obj1_para.add_run(". Establish comprehensive comparison framework between RO-derived observations and E3SM model output, including analysis of E3SM's parameterized gravity wave momentum fluxes and their relationship to observed GW activity. Develop advanced techniques for estimating GW momentum flux from both observations and E3SM output, including uncertainty quantification and quality control procedures. This objective creates the foundation for robust model-observation comparison and establishes E3SM's baseline performance in representing MJO-GW relationships.")
    
    # Objective 2
    obj2_heading = doc.add_heading('Objective 2: Characterize Global MJO-GW Climatology and E3SM Comparison (Months 8-20)', level=2)
    
    obj2_para = doc.add_paragraph()
    obj2_para.add_run("Create the first comprehensive global climatology of stratospheric GW activity modulated by the MJO using validated observational parameters, with systematic comparison to E3SM model representations. Categorize MJO events by propagation types following established methodologies")
    add_superscript_reference(obj2_para, 12)
    obj2_para.add_run(" and analyze corresponding GW activity in both observations and E3SM output for each type. Investigate interactive modulation by QBO and ENSO in both observational data and E3SM simulations, identifying specific areas where the model captures or fails to represent observed relationships. Quantify systematic differences between E3SM's parameterized gravity wave effects and observational estimates across different MJO phases and propagation characteristics.")
    
    # Objective 3
    obj3_heading = doc.add_heading('Objective 3: Comprehensive Climate Model Evaluation and E3SM Assessment (Months 15-24)', level=2)
    
    obj3_para = doc.add_paragraph()
    obj3_para.add_run("Conduct comprehensive evaluation of E3SM and other state-of-the-art climate models (CMIP6/CMIP7) using the observational climatology as benchmark. Evaluate model performance using established metrics")
    add_superscript_reference(obj3_para, 13)
    obj3_para.add_run(" and assess systematic biases in representing MJO-GW relationships. Provide quantitative assessment of E3SM's competitive position relative to other leading climate models and identify specific areas for potential model development priorities. Develop recommendations for E3SM evaluation protocols and provide observational constraints that can inform future model development efforts. This objective delivers critical insights into E3SM's performance while contributing to the broader climate modeling community's understanding of MJO-gravity wave interactions.")
    
    return doc

if __name__ == "__main__":
    # Create the harmonized document
    doc = create_harmonized_proposal()
    doc = add_abstract_section(doc)
    doc = add_background_section(doc)
    doc = add_research_questions_section(doc)
    doc = add_objectives_section(doc)
    
    # Save the document
    doc.save('Harmonized_LLNL_MJO_Proposal_E3SM_Validation.docx')
    print("Harmonized LLNL proposal document created successfully!")
