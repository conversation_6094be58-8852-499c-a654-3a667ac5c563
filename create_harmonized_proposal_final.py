#!/usr/bin/env python3
"""
Harmonized LLNL Lawrence Fellowship Proposal Generator - Final Part
Adds work packages, timeline, and references
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH

def add_superscript_reference(paragraph, ref_num):
    """Add superscript reference number to paragraph"""
    run = paragraph.add_run(str(ref_num))
    run.font.superscript = True
    run.font.size = Pt(8)

def add_work_packages_section(doc):
    """Add the Work Packages and Timeline section"""
    
    # Work Packages
    wp_heading = doc.add_heading('Work Packages and Timeline', level=1)
    
    wp_intro = doc.add_paragraph()
    wp_intro.add_run("The project is structured into four Work Packages (WPs) that logically progress through the three research objectives over the 24-month fellowship, with specific emphasis on E3SM model validation and observational analysis:")
    
    # WP1
    wp1_heading = doc.add_heading('WP1: Observational Baseline and E3SM Validation Framework (Months 1-12)', level=2)
    
    wp1_para = doc.add_paragraph()
    wp1_para.add_run("Establish comprehensive validation framework for RO observations through process-based case studies and statistical comparisons with independent satellite measurements (TIMED/SABER, Aura/MLS). Develop robust methodologies for extracting GW parameters from both RO data and E3SM model output, including uncertainty quantification and quality control procedures. Create systematic comparison framework between observations and E3SM parameterized gravity wave effects. Validate GW separation methodology and assess representativeness of RO-observed GWs relative to E3SM's parameterization assumptions.")
    
    wp1_deliverable = doc.add_paragraph()
    wp1_deliverable.add_run("Deliverable D1.1 (M12): Comprehensive validation report detailing case study and statistical comparisons, global climatology of GW potential energy and momentum flux from RO observations, and baseline assessment of E3SM performance in representing MJO-GW relationships.")
    
    # WP2
    wp2_heading = doc.add_heading('WP2: MJO-GW Climatology and E3SM Comparison (Months 10-18)', level=2)
    
    wp2_para = doc.add_paragraph()
    wp2_para.add_run("Develop comprehensive global climatology of GW activity modulated by MJO using validated methods from WP1. Categorize MJO events by propagation types (standing, jumping, slow/fast-propagating) and composite corresponding GW anomalies for each type in both observations and E3SM output. Investigate interactive modulation by QBO and ENSO in observations and E3SM simulations. Quantify systematic differences between E3SM's parameterized gravity wave effects and observational estimates across different MJO phases and climate states.")
    
    wp2_deliverable = doc.add_paragraph()
    wp2_deliverable.add_run("Deliverable D2.1 (M18): Observationally-derived global climatology of MJO-modulated GW activity composited by MJO propagation type, systematic comparison with E3SM representations, and quantitative assessment of model-observation differences.")
    
    # WP3
    wp3_heading = doc.add_heading('WP3: Comprehensive Climate Model Evaluation (Months 16-22)', level=2)
    
    wp3_para = doc.add_paragraph()
    wp3_para.add_run("Use observational climatology from WP2 as benchmark to evaluate E3SM and other state-of-the-art climate models (CMIP6/CMIP7). Perform composite analysis on model outputs to assess representation of MJO-GW momentum flux relationships. Apply established metrics to evaluate model skill in representing MJO variability and corresponding signals in gravity wave parameterizations. Assess E3SM's competitive position relative to other leading climate models and identify specific areas for potential development priorities.")
    
    wp3_deliverable = doc.add_paragraph()
    wp3_deliverable.add_run("Deliverable D3.1 (M22): Quantitative assessment of E3SM and CMIP models' ability to reproduce MJO-modulated GW relationships, identification of systematic biases in current parameterization schemes, and recommendations for E3SM evaluation protocols.")
    
    # WP4
    wp4_heading = doc.add_heading('WP4: Dissemination and Reporting (Months 1-24)', level=2)
    
    wp4_para = doc.add_paragraph()
    wp4_para.add_run("Ongoing activity throughout the project including presentation of preliminary results at conferences, preparation and submission of manuscripts for publication, archiving of data products according to FAIR principles, and completion of final project reporting. Engage with E3SM development community to communicate findings and provide observational constraints for model evaluation.")
    
    wp4_deliverable = doc.add_paragraph()
    wp4_deliverable.add_run("Deliverable D4.1 (M24): Final project report, 2-3 peer-reviewed publications, publicly available datasets, and comprehensive documentation of methodologies and results.")
    
    return doc

def add_expected_outcomes_section(doc):
    """Add the Expected Outcomes section"""
    
    # Expected Outcomes
    outcomes_heading = doc.add_heading('Expected Outcomes and Deliverables', level=1)
    
    # Scientific Deliverables
    sci_deliverables_heading = doc.add_heading('Scientific Deliverables', level=2)
    
    sci_deliverables = [
        "• First comprehensive global climatology of MJO-modulated stratospheric gravity wave activity using multi-mission RO data (2006-2024)",
        "• Systematic validation of E3SM's gravity wave parameterizations using global observational constraints",
        "• Quantitative assessment of E3SM's performance in representing MJO-related processes and stratosphere-troposphere coupling",
        "• Advanced methodologies for model-observation comparison applicable to climate model evaluation",
        "• Observational constraints for improving understanding of multi-scale atmospheric interactions in climate models"
    ]
    
    for deliverable in sci_deliverables:
        deliv_para = doc.add_paragraph()
        deliv_para.add_run(deliverable)
    
    # LLNL Strategic Benefits
    llnl_benefits_heading = doc.add_heading('LLNL Strategic Benefits', level=2)
    
    llnl_benefits = [
        "• Critical validation data for E3SM model development and performance assessment",
        "• Enhanced understanding of E3SM's capabilities and limitations in representing atmospheric processes",
        "• Strengthened LLNL leadership in Earth system science through cutting-edge observational analysis",
        "• Advanced computational science capabilities for large-scale climate data analysis",
        "• Improved understanding of atmospheric prediction capabilities relevant to national security applications"
    ]
    
    for benefit in llnl_benefits:
        benefit_para = doc.add_paragraph()
        benefit_para.add_run(benefit)
    
    # Publications and Data Products
    pubs_heading = doc.add_heading('Publications and Data Products', level=2)
    
    pubs_para = doc.add_paragraph()
    pubs_para.add_run("The research will produce 2-3 high-impact peer-reviewed publications in leading atmospheric science journals (e.g., Journal of Geophysical Research: Atmospheres, Atmospheric Chemistry and Physics, Geophysical Research Letters, Journal of Climate). All datasets will be made publicly available following FAIR data principles, with comprehensive documentation and metadata. Processing scripts and analysis codes will be made available to ensure reproducibility and enable community use of the methodologies developed.")
    
    return doc

def add_risk_assessment_section(doc):
    """Add the Risk Assessment section"""
    
    # Risk Assessment
    risk_heading = doc.add_heading('Risk Assessment and Mitigation', level=1)
    
    risk_intro = doc.add_paragraph()
    risk_intro.add_run("Potential risks have been identified with proactive mitigation strategies:")
    
    risks = [
        "• Data Quality Issues (Low Risk): Comprehensive quality control procedures and multiple validation approaches minimize impact of data quality problems. Alternative datasets available if needed.",
        "• E3SM Data Access (Low Risk): Established collaboration with LLNL E3SM team ensures reliable access to model output. Alternative CMIP6/CMIP7 models available for comparison if needed.",
        "• Computational Resources (Low Risk): LLNL's world-class computing facilities provide sufficient resources. Analysis methods designed to be computationally efficient.",
        "• Methodology Challenges (Medium Risk): Extensive validation framework and multiple approaches reduce risk of methodological issues. Regular consultation with supervisor and E3SM experts.",
        "• Timeline Delays (Low Risk): Overlapping work packages and flexible scheduling allow for adjustments. Critical path analysis ensures key deliverables remain on schedule."
    ]
    
    for risk in risks:
        risk_para = doc.add_paragraph()
        risk_para.add_run(risk)
    
    return doc

def add_references_section(doc):
    """Add the References section"""
    
    # References
    ref_heading = doc.add_heading('References', level=1)
    
    references = [
        "1. Zhang, C. Madden-Julian oscillation. Rev. Geophys. 43, RG2003 (2005).",
        "2. Madden, R. A. & Julian, P. R. Observations of the 40–50-day tropical oscillation—a review. Mon. Weather Rev. 122, 814–837 (1994).",
        "3. Guo, Y., Wen, M., Li, T. & Ren, X. Variations in Northern Hemisphere storm track and extratropical cyclone activity associated with the Madden-Julian oscillation. J. Climate 30, 4799–4818 (2017).",
        "4. Baggett, C. F., Lee, S. & Feldstein, S. B. An investigation of the influence of atmospheric rivers on cold-season extratropical cyclones. Mon. Weather Rev. 145, 4019–4034 (2017).",
        "5. Henderson, S. A., Maloney, E. D. & Barnes, E. A. The influence of the Madden-Julian oscillation on Northern Hemisphere winter blocking. J. Climate 29, 4597–4616 (2016).",
        "6. Alexander, M. J. et al. Recent developments in gravity-wave effects in climate models. Q. J. R. Meteorol. Soc. 136, 1103–1124 (2010).",
        "7. Fritts, D. C. & Alexander, M. J. Gravity wave dynamics and effects in the middle atmosphere. Rev. Geophys. 41, 1003 (2003).",
        "8. Bushell, A. C. et al. Evaluation of the quasi-biennial oscillation in global climate models for the SPARC QBO-initiative. Q. J. R. Meteorol. Soc. 148, 1459–1489 (2022).",
        "9. Richter, J. H. et al. Progress in simulating the quasi-biennial oscillation in CMIP models. J. Geophys. Res. Atmos. 125, e2019JD032362 (2020).",
        "10. Alexander, M. J. & Grimsdell, A. W. Global estimates of gravity wave momentum flux from High Resolution Dynamics Limb Sounder observations. J. Geophys. Res. Atmos. 118, 6988–7007 (2013).",
        "11. Wu, D. L. et al. Global gravity wave variances from Aura MLS. Geophys. Res. Lett. 33, L07809 (2006).",
        "12. Back, S. Y. et al. MJO diversity in CMIP6 models. J. Climate 37, 4835–4850 (2024).",
        "13. Gerber, E. P. et al. The Dynamics and Variability Model Intercomparison Project (DynVarMIP) for CMIP6. Geosci. Model Dev. 9, 3413–3425 (2016)."
    ]
    
    for ref in references:
        ref_para = doc.add_paragraph()
        ref_para.add_run(ref)
    
    return doc

def add_conclusion_section(doc):
    """Add the Conclusion section"""
    
    # Conclusion
    conclusion_heading = doc.add_heading('Conclusion', level=1)
    
    conclusion_para = doc.add_paragraph()
    conclusion_para.add_run("This research represents a critical contribution to LLNL's Earth system modeling capabilities through comprehensive observational validation of E3SM's atmospheric physics. By leveraging the extensive multi-mission radio occultation dataset to characterize MJO-modulated gravity wave activity, the project will provide unprecedented observational constraints for evaluating E3SM's performance in representing stratosphere-troposphere coupling processes. The systematic comparison between observations and E3SM output will identify specific areas where the model excels and areas requiring attention, supporting LLNL's leadership in climate science while advancing fundamental understanding of atmospheric dynamics. The research directly supports LLNL's mission priorities in Earth system science, computational modeling, and national security applications, while contributing valuable insights to the broader climate modeling community.")
    
    return doc

if __name__ == "__main__":
    # Load the existing document
    doc = Document('Harmonized_LLNL_MJO_Proposal_E3SM_Validation_Complete.docx')
    
    # Add final sections
    doc = add_work_packages_section(doc)
    doc = add_expected_outcomes_section(doc)
    doc = add_risk_assessment_section(doc)
    doc = add_references_section(doc)
    doc = add_conclusion_section(doc)
    
    # Save the final document
    doc.save('Harmonized_LLNL_MJO_Proposal_E3SM_Validation_Final.docx')
    print("Final harmonized LLNL proposal document created successfully!")
